#!/usr/bin/env python3
"""
Test script to verify that ARINC 424 record parsing improvements are working correctly.
This script checks that all section codes in the flyright.pc file are now being recognized.
"""

import sys
import os
import json
from collections import defaultdict, Counter

# Add src directory to path
sys.path.insert(0, 'src')

try:
    import arinc424
    print("✓ ARINC 424 library imported successfully")
except ImportError as e:
    print(f"✗ Failed to import ARINC 424 library: {e}")
    sys.exit(1)

def test_section_code_coverage():
    """Test that all section codes in the file are being parsed successfully."""
    
    print("\n=== Testing Section Code Coverage ===")
    
    # Count section codes in the raw file
    raw_section_codes = Counter()
    parsed_section_codes = Counter()
    failed_lines = []
    total_lines = 0
    header_lines = 0
    
    with open("flyright.pc", "r") as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            total_lines += 1
            
            # Skip header records
            if line.startswith("HDR"):
                header_lines += 1
                continue
            
            # Extract raw section code (positions 5-6)
            if len(line) >= 6:
                raw_section_code = line[4:6]
                raw_section_codes[raw_section_code] += 1
            
            # Try to parse with Python library
            record = arinc424.Record()
            if record.read(line):
                try:
                    json_str = record.json(output=False)
                    data = json.loads(json_str)
                    section_code = data.get('Section Code', 'UNKNOWN')
                    parsed_section_codes[section_code] += 1
                except (json.JSONDecodeError, Exception) as e:
                    failed_lines.append((line_num, line[:50], str(e)))
            else:
                failed_lines.append((line_num, line[:50], "Failed to parse"))
    
    print(f"Total lines processed: {total_lines}")
    print(f"Header lines (expected to fail): {header_lines}")
    print(f"Data lines: {total_lines - header_lines}")
    print(f"Successfully parsed records: {sum(parsed_section_codes.values())}")
    print(f"Failed to parse: {len(failed_lines)}")
    
    print(f"\nRaw section codes found in file:")
    for code, count in sorted(raw_section_codes.items()):
        print(f"  {repr(code)}: {count}")
    
    print(f"\nParsed section codes:")
    for code, count in sorted(parsed_section_codes.items()):
        print(f"  {repr(code)}: {count}")
    
    if failed_lines:
        print(f"\nFirst 10 failed lines:")
        for line_num, line_preview, error in failed_lines[:10]:
            print(f"  Line {line_num}: {line_preview}... - {error}")
    
    # Calculate success rate
    data_lines = total_lines - header_lines
    success_rate = (sum(parsed_section_codes.values()) / data_lines) * 100 if data_lines > 0 else 0
    
    print(f"\nParsing success rate: {success_rate:.2f}%")
    
    return success_rate > 95  # Consider 95%+ success rate as good

def test_specific_record_types():
    """Test parsing of specific record types that were previously problematic."""
    
    print("\n=== Testing Specific Record Types ===")
    
    test_records = [
        # VHF Navaid (D followed by space)
        ('SCAND        6G    CY111380 DU                     6G  N52105227W113525757     029741  407NARRED DEER                      000011204', 'D '),
        # Airport (PA)
        ('SCANP CYOWCYAYOW     010000100Y N45192101W075400217W014000377250YOW CY1800018000CR00YMNAR    MACDONALD-CARTIER INTL        031651812', 'PA'),
        # Terminal Waypoint (PC)
        ('SCANP CYOWCYCADVOS CY0    CIZ   N45201159W075235384                       W0130     NAR           ADVOS                    031661803', 'PC'),
        # SID (PD)
        ('SCANP CYOWCYDCYOW01   0A0    SZ   N45192101W075400217                       W0140     NAR           CYOW01                   031671803', 'PD'),
    ]
    
    all_passed = True
    
    for record_line, expected_section in test_records:
        record = arinc424.Record()
        if record.read(record_line):
            try:
                json_str = record.json(output=False)
                data = json.loads(json_str)
                actual_section = data.get('Section Code', 'UNKNOWN')
                
                if actual_section == expected_section:
                    print(f"✓ {expected_section} record parsed correctly")
                else:
                    print(f"✗ {expected_section} record: expected {expected_section}, got {actual_section}")
                    all_passed = False
            except Exception as e:
                print(f"✗ {expected_section} record: JSON parsing failed - {e}")
                all_passed = False
        else:
            print(f"✗ {expected_section} record: Failed to parse")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    
    print("ARINC 424 Parsing Improvements Test")
    print("=" * 40)
    
    # Test section code coverage
    coverage_passed = test_section_code_coverage()
    
    # Test specific record types
    specific_passed = test_specific_record_types()
    
    print("\n" + "=" * 40)
    print("TEST RESULTS:")
    print(f"Section code coverage: {'PASS' if coverage_passed else 'FAIL'}")
    print(f"Specific record types: {'PASS' if specific_passed else 'FAIL'}")
    
    overall_result = coverage_passed and specific_passed
    print(f"Overall result: {'PASS' if overall_result else 'FAIL'}")
    
    if overall_result:
        print("\n✓ All tests passed! ARINC 424 parsing improvements are working correctly.")
    else:
        print("\n✗ Some tests failed. Please review the output above.")
    
    return 0 if overall_result else 1

if __name__ == "__main__":
    sys.exit(main())
